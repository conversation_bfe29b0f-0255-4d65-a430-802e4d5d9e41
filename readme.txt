This program shows how to use OLE automation and C# to control PowerDesigner from outside of PowerDesigner.
This program allows you to create a PowerDesigner PDM model. 

To communicate with PowerDesign<PERSON>, this program creates a PowerDesigner application object using COM.

To run this program, you need to install the .NET Framework or the .NET Framework SDK.
You can download the .NET Framework from the Microsoft Web site.

This program needs to access the Sybase PdCommon and Sybase PdPDM type libraries.
You need to add these type libraries in the list of library references in Visual Studio .NET:
- Right click on the C# project node
- Select References
- Click on the COM tab
- Click on the Sybase PdCommon 15.3 Type Library, then click on the Select button
- Click on the Sybase PdPDM 15.3 Type Library, then click on the Select button
- Click on OK
Visual Studio .NET will create the Interop.PdCommon.dll and Interop.PdPDM.dll wrapper DLLs for PowerDesigner 15.

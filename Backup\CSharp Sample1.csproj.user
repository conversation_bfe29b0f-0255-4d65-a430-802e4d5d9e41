<VisualStudioProject>
    <CSHARP LastOpenVersion = "7.10.3077" >
        <Build>
            <Settings ReferencePath = "" >
                <Config
                    Name = "Debug"
                    EnableASPDebugging = "false"
                    EnableASPXDebugging = "false"
                    EnableUnmanagedDebugging = "false"
                    EnableSQLServerDebugging = "false"
                    RemoteDebugEnabled = "false"
                    RemoteDebugMachine = ""
                    StartAction = "Project"
                    StartArguments = ""
                    StartPage = ""
                    StartProgram = ""
                    StartURL = ""
                    StartWorkingDirectory = ""
                    StartWithIE = "true"
                />
                <Config
                    Name = "Release"
                    EnableASPDebugging = "false"
                    EnableASPXDebugging = "false"
                    EnableUnmanagedDebugging = "false"
                    EnableSQLServerDebugging = "false"
                    RemoteDebugEnabled = "false"
                    RemoteDebugMachine = ""
                    StartAction = "Project"
                    StartArguments = ""
                    StartPage = ""
                    StartProgram = ""
                    StartURL = ""
                    StartWorkingDirectory = ""
                    StartWithIE = "true"
                />
            </Settings>
        </Build>
        <OtherProjectSettings
            CopyProjectDestinationFolder = ""
            CopyProjectUncPath = ""
            CopyProjectOption = "0"
            ProjectView = "ProjectFiles"
            ProjectTrust = "0"
        />
    </CSHARP>
</VisualStudioProject>


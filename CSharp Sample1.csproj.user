﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LastOpenVersion>7.10.3077</LastOpenVersion>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ReferencePath />
    <CopyProjectDestinationFolder />
    <CopyProjectUncPath />
    <CopyProjectOption>0</CopyProjectOption>
    <ProjectView>ProjectFiles</ProjectView>
    <ProjectTrust>0</ProjectTrust>
    <PublishUrlHistory />
    <InstallUrlHistory />
    <SupportUrlHistory />
    <UpdateUrlHistory />
    <BootstrapperUrlHistory />
    <ErrorReportUrlHistory />
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <EnableASPDebugging>false</EnableASPDebugging>
    <EnableASPXDebugging>false</EnableASPXDebugging>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <EnableSQLServerDebugging>false</EnableSQLServerDebugging>
    <RemoteDebugEnabled>false</RemoteDebugEnabled>
    <RemoteDebugMachine />
    <StartAction>Project</StartAction>
    <StartArguments />
    <StartPage />
    <StartProgram />
    <StartURL />
    <StartWorkingDirectory />
    <StartWithIE>true</StartWithIE>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <EnableASPDebugging>false</EnableASPDebugging>
    <EnableASPXDebugging>false</EnableASPXDebugging>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <EnableSQLServerDebugging>false</EnableSQLServerDebugging>
    <RemoteDebugEnabled>false</RemoteDebugEnabled>
    <RemoteDebugMachine />
    <StartAction>Project</StartAction>
    <StartArguments />
    <StartPage />
    <StartProgram />
    <StartURL />
    <StartWorkingDirectory />
    <StartWithIE>true</StartWithIE>
  </PropertyGroup>
</Project>
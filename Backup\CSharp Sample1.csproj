<VisualStudioProject>
    <CSHARP
        ProjectType = "Local"
        ProductVersion = "7.10.3077"
        SchemaVersion = "2.0"
        ProjectGuid = "{8955DC9E-EC8D-4825-AC5E-35A90A42666D}"
    >
        <Build>
            <Settings
                ApplicationIcon = "App.ico"
                AssemblyKeyContainerName = ""
                AssemblyName = "CSharp Sample1"
                AssemblyOriginatorKeyFile = ""
                DefaultClientScript = "JScript"
                DefaultHTMLPageLayout = "Grid"
                DefaultTargetSchema = "IE50"
                DelaySign = "false"
                OutputType = "WinExe"
                PreBuildEvent = ""
                PostBuildEvent = ""
                RootNamespace = "CSharp_Sample1"
                RunPostBuildEvent = "OnBuildSuccess"
                StartupObject = ""
            >
                <Config
                    Name = "Debug"
                    AllowUnsafeBlocks = "false"
                    BaseAddress = "285212672"
                    CheckForOverflowUnderflow = "false"
                    ConfigurationOverrideFile = ""
                    DefineConstants = "DEBUG;TRACE"
                    DocumentationFile = ""
                    DebugSymbols = "true"
                    FileAlignment = "4096"
                    IncrementalBuild = "true"
                    NoStdLib = "false"
                    NoWarn = ""
                    Optimize = "false"
                    OutputPath = "bin\Debug\"
                    RegisterForComInterop = "false"
                    RemoveIntegerChecks = "false"
                    TreatWarningsAsErrors = "false"
                    WarningLevel = "4"
                />
                <Config
                    Name = "Release"
                    AllowUnsafeBlocks = "false"
                    BaseAddress = "285212672"
                    CheckForOverflowUnderflow = "false"
                    ConfigurationOverrideFile = ""
                    DefineConstants = "TRACE"
                    DocumentationFile = ""
                    DebugSymbols = "false"
                    FileAlignment = "4096"
                    IncrementalBuild = "false"
                    NoStdLib = "false"
                    NoWarn = ""
                    Optimize = "true"
                    OutputPath = "bin\Release\"
                    RegisterForComInterop = "false"
                    RemoveIntegerChecks = "false"
                    TreatWarningsAsErrors = "false"
                    WarningLevel = "4"
                />
            </Settings>
            <References>
                <Reference
                    Name = "System"
                    AssemblyName = "System"
                    HintPath = "C:\WINDOWS\Microsoft.NET\Framework\v1.0.3705\System.dll"
                />
                <Reference
                    Name = "System.Data"
                    AssemblyName = "System.Data"
                    HintPath = "C:\WINDOWS\Microsoft.NET\Framework\v1.0.3705\System.Data.dll"
                />
                <Reference
                    Name = "System.Drawing"
                    AssemblyName = "System.Drawing"
                    HintPath = "C:\WINDOWS\Microsoft.NET\Framework\v1.0.3705\System.Drawing.dll"
                />
                <Reference
                    Name = "System.Windows.Forms"
                    AssemblyName = "System.Windows.Forms"
                    HintPath = "C:\WINDOWS\Microsoft.NET\Framework\v1.0.3705\System.Windows.Forms.dll"
                />
                <Reference
                    Name = "System.XML"
                    AssemblyName = "System.Xml"
                    HintPath = "C:\WINDOWS\Microsoft.NET\Framework\v1.0.3705\System.XML.dll"
                />
                <Reference
                    Name = "PdCommon"
                    Guid = "{A552E368-E3AD-452B-A703-9A2199B008D9}"
                    VersionMajor = "15"
                    VersionMinor = "3"
                    Lcid = "0"
                    WrapperTool = "tlbimp"
                />
                <Reference
                    Name = "PdPDM"
                    Guid = "{B18A8271-9633-11D1-9914-006097355D9B}"
                    VersionMajor = "15"
                    VersionMinor = "3"
                    Lcid = "0"
                    WrapperTool = "tlbimp"
                />
            </References>
        </Build>
        <Files>
            <Include>
                <File
                    RelPath = "App.ico"
                    BuildAction = "Content"
                />
                <File
                    RelPath = "AssemblyInfo.cs"
                    SubType = "Code"
                    BuildAction = "Compile"
                />
                <File
                    RelPath = "Form1.cs"
                    SubType = "Form"
                    BuildAction = "Compile"
                />
                <File
                    RelPath = "Form1.resx"
                    DependentUpon = "Form1.cs"
                    BuildAction = "EmbeddedResource"
                />
            </Include>
        </Files>
    </CSHARP>
</VisualStudioProject>

